import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Socket } from 'socket.io';
import { RoomManagerService, RoomType } from '../services/room-manager.service';
import {
  UserTypingDto,
  MessageReadDto,
  DeleteMessageDto,
  DeleteMessagesForEveryoneDto,
  ChatType,
  normalizeChatId,
  SocketSendMessageDto,
  SendMessageDto,
} from '../dto/chat-operation.dto';
import { SendPrivateMessageDto } from '../../../modules/messages/dto/send-private-message.dto';
import { EVENT_NAMES } from '../../../common/constants/event-names';

@Injectable()
export class ChatOperationHandler {
  private readonly logger = new Logger(ChatOperationHandler.name);

  constructor(
    private readonly roomManager: RoomManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private isSendMessageDto(dto: SocketSendMessageDto): dto is SendMessageDto {
    return 'content' in dto;
  }

  private isSendPrivateMessageDto(
    dto: SocketSendMessageDto,
  ): dto is SendPrivateMessageDto {
    return 'encryptedContent' in dto;
  }

  async handleSendMessage(
    client: Socket,
    dto: SocketSendMessageDto,
  ): Promise<void> {
    try {
      const memberId = client.data.memberId;
      if (!memberId) {
        throw new Error('Member ID not found in client data');
      }

      // Validate and normalize chat ID
      const { normalizedId, identifiers } = normalizeChatId(
        dto.chatType,
        dto.chatId,
      );

      // Check if client is in the appropriate room
      const roomType =
        dto.chatType === ChatType.GROUP ? RoomType.GROUP : RoomType.PRIVATE;
      const isInRoom = this.roomManager.isClientInRoom(
        client,
        roomType,
        ...identifiers,
      );

      if (!isInRoom) {
        throw new Error(`You are not a member of this ${dto.chatType}`);
      }

      // Handle different message types - SendMessageDto has 'content', SendPrivateMessageDto has 'encryptedContent'
      const content = this.isSendMessageDto(dto)
        ? dto.content
        : this.isSendPrivateMessageDto(dto)
          ? dto.encryptedContent
          : '';
      const metadata = this.isSendMessageDto(dto)
        ? dto.metadata
        : this.isSendPrivateMessageDto(dto)
          ? dto.encryptedMetaData
          : undefined;

      // Emit event to feature module for message processing
      await this.eventEmitter.emitAsync(EVENT_NAMES.SEND_MESSAGE_REQUESTED, {
        chatType: dto.chatType,
        chatId: normalizedId,
        senderId: memberId,
        content: content,
        replyToMessageId: dto.replyToMessageId,
        nonce: dto.nonce,
        metadata: metadata,
        sentAt: dto.sentAt,
        socketId: client.id,
      });

      // Acknowledge to sender immediately
      client.emit('message_sent_ack', {
        chatType: dto.chatType,
        chatId: normalizedId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling send message:', error);
      client.emit('error', { message: error.message });
    }
  }

  async handleUserTyping(client: Socket, dto: UserTypingDto): Promise<void> {
    try {
      const memberId = client.data.memberId;
      if (!memberId) {
        throw new Error('Member ID not found in client data');
      }

      const { normalizedId, identifiers } = normalizeChatId(
        dto.chatType,
        dto.chatId,
      );
      const roomType =
        dto.chatType === ChatType.GROUP ? RoomType.GROUP : RoomType.PRIVATE;

      // Check if client is in the appropriate room
      const isInRoom = this.roomManager.isClientInRoom(
        client,
        roomType,
        ...identifiers,
      );
      if (!isInRoom) {
        this.logger.warn(
          `Member ${memberId} trying to send typing indicator to ${dto.chatType} they're not in`,
        );
        return;
      }

      // Emit typing indicator to all room members except sender
      const typingData = {
        chatType: dto.chatType,
        chatId: normalizedId,
        memberId,
        isTyping: dto.isTyping,
        timestamp: new Date().toISOString(),
      };

      // Get room name and emit to all except sender
      const roomName = this.roomManager.generateRoomName(
        roomType,
        ...identifiers,
      );
      client.to(roomName).emit('user_typing', typingData);

      // Optionally emit event for other modules to handle typing indicators
      this.eventEmitter.emit(EVENT_NAMES.USER_TYPING, {
        chatType: dto.chatType,
        chatId: normalizedId,
        memberId,
        isTyping: dto.isTyping,
        duration: dto.duration,
      });
    } catch (error) {
      this.logger.error('Error handling user typing:', error);
      throw error;
    }
  }

  async handleMessageRead(client: Socket, dto: MessageReadDto): Promise<void> {
    try {
      const memberId = client.data.memberId;
      const readerId = dto.readerId || memberId;

      if (!memberId) {
        throw new Error('Member ID not found in client data');
      }

      const { normalizedId, identifiers } = normalizeChatId(
        dto.chatType,
        dto.chatId,
      );
      const roomType =
        dto.chatType === ChatType.GROUP ? RoomType.GROUP : RoomType.PRIVATE;

      // Check if client is in the appropriate room
      const isInRoom = this.roomManager.isClientInRoom(
        client,
        roomType,
        ...identifiers,
      );
      if (!isInRoom) {
        throw new Error(`You are not a member of this ${dto.chatType}`);
      }

      // Emit event to feature module for read processing
      await this.eventEmitter.emitAsync(EVENT_NAMES.MESSAGE_READ_REQUESTED, {
        chatType: dto.chatType,
        chatId: normalizedId,
        readerId,
        messageIds: dto.messageIds,
        readAt: new Date(),
      });

      // Acknowledge to reader
      client.emit('message_read_ack', {
        chatType: dto.chatType,
        chatId: normalizedId,
        messageIds: dto.messageIds,
        readAt: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling message read:', error);
      throw error;
    }
  }

  async handleDeleteMessageForMe(
    client: Socket,
    dto: DeleteMessageDto,
  ): Promise<void> {
    try {
      const memberId = client.data.memberId;
      const deleterId = dto.deleterId || memberId;

      if (!memberId) {
        throw new Error('Member ID not found in client data');
      }

      const { normalizedId, identifiers } = normalizeChatId(
        dto.chatType,
        dto.chatId,
      );
      const roomType =
        dto.chatType === ChatType.GROUP ? RoomType.GROUP : RoomType.PRIVATE;

      // Check if client is in the appropriate room
      const isInRoom = this.roomManager.isClientInRoom(
        client,
        roomType,
        ...identifiers,
      );
      if (!isInRoom) {
        throw new Error(`You are not a member of this ${dto.chatType}`);
      }

      // Emit event to feature module for deletion processing
      await this.eventEmitter.emitAsync(
        EVENT_NAMES.DELETE_MESSAGE_FOR_ME_REQUESTED,
        {
          chatType: dto.chatType,
          chatId: normalizedId,
          deleterId,
          messageId: dto.messageId,
          deletedAt: new Date(),
        },
      );

      // Acknowledge to deleter (only affects them)
      client.emit('message_deleted_for_me', {
        chatType: dto.chatType,
        chatId: normalizedId,
        messageId: dto.messageId,
        deletedAt: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling delete message for me:', error);
      throw error;
    }
  }

  async handleDeleteMessagesForEveryone(
    client: Socket,
    dto: DeleteMessagesForEveryoneDto,
  ): Promise<void> {
    try {
      const memberId = client.data.memberId;
      const deleterId = dto.deleterId || memberId;

      if (!memberId) {
        throw new Error('Member ID not found in client data');
      }

      const { normalizedId, identifiers } = normalizeChatId(
        dto.chatType,
        dto.chatId,
      );
      const roomType =
        dto.chatType === ChatType.GROUP ? RoomType.GROUP : RoomType.PRIVATE;

      // Check if client is in the appropriate room
      const isInRoom = this.roomManager.isClientInRoom(
        client,
        roomType,
        ...identifiers,
      );
      if (!isInRoom) {
        throw new Error(`You are not a member of this ${dto.chatType}`);
      }

      // Emit event to feature module for deletion processing
      // The feature module will validate if user has permission to delete for everyone
      await this.eventEmitter.emitAsync(
        EVENT_NAMES.DELETE_MESSAGES_FOR_EVERYONE_REQUESTED,
        {
          chatType: dto.chatType,
          chatId: normalizedId,
          deleterId,
          messageIds: dto.messageIds,
          deletedAt: new Date(),
        },
      );

      // Acknowledge to deleter
      client.emit('messages_delete_for_everyone_ack', {
        chatType: dto.chatType,
        chatId: normalizedId,
        messageIds: dto.messageIds,
        deletedAt: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling delete messages for everyone:', error);
      throw error;
    }
  }
}
