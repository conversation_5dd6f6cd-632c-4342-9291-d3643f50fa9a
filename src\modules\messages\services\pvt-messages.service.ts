import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';
import { PrivateMessage } from '../entities/private-message.entity';
import { SendPrivateMessageDto } from '../dto/send-private-message.dto';
import { PrivateMessageSentEvent } from 'src/common/events/private-message.events';
import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';
import { MessageThreadService } from './message-threads.service';
import { StorageService } from 'src/core/storage/storage.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { UsersService } from 'src/modules/users/services/users.service';
import { QueryRunner } from 'typeorm'; // Import QueryRunner from TypeORM

@Injectable()
export class PrivateMessageService {
  private readonly logger = new Logger(PrivateMessageService.name);

  constructor(
    @InjectRepository(PrivateMessage)
    private readonly privateMessageRepo: Repository<PrivateMessage>,
    private readonly eventBus: EventBusService,
    private readonly messageThreadService: MessageThreadService,
    private readonly storageService: StorageService,
    private readonly userService: UsersService,
  ) {}

  async saveAndBroadcastMessage(
    sendMessageDto: SendPrivateMessageDto,
    excludeSenderId?: boolean,
    retryCount?: number,
  ): Promise<PrivateMessage> {
    // Save message first to get sequence number
    const savedMessage = await this.saveMessage(sendMessageDto);

    const retryCounts = retryCount ?? 0;
    const targetMembers: number[] = excludeSenderId
      ? [sendMessageDto.receiverId]
      : [sendMessageDto.senderId, sendMessageDto.receiverId];

    const memberNotificationInfo: MemberNotificationInfo[] = [];

    for (const memberId of targetMembers) {
      const thread = await this.messageThreadService.getThreadByTargetId(
        memberId,
        ChatType.PRIVATE,
        sendMessageDto.senderId,
      );
      const isMuted = thread?.isMuted || false;
      memberNotificationInfo.push({
        memberId,
        silent: isMuted,
      });
    }

    // Get sender's profile picture for notification
    const sender = await this.userService.findById(sendMessageDto.senderId);

    // Prepare message data for broadcasting
    const messageData = {
      id: savedMessage.id,
      seq: savedMessage.seq,
      chatId: savedMessage.chatId,
      senderId: savedMessage.senderId,
      receiverId: savedMessage.receiverId,
      encryptedContent: savedMessage.encryptedContent,
      nonce: savedMessage.nonce,
      sentAt: savedMessage.sentAt,
      sender: savedMessage.sender,
      receiver: savedMessage.receiver,
      replyTo: savedMessage.replyTo,
    };

    const notificationPayload: PushNotification = {
      title: savedMessage.sender.name,
      body: `${savedMessage.sender.name} sent you a message`,
      data: messageData,
      imageUrl: sender?.imageUrl,
    };

    // Publish event for message delivery
    this.eventBus.publish(
      new PrivateMessageSentEvent(
        savedMessage.id,
        savedMessage.senderId,
        savedMessage.receiverId,
        memberNotificationInfo,
        notificationPayload,
        retryCounts,
      ),
    );

    return savedMessage;
  }



  async getNextSequenceNumber(
    senderId: number,
    receiverId: number,
    queryRunner?: QueryRunner, // Optional if used
  ): Promise<number> {
    const chatId = PrivateMessageService.generateChatId(senderId, receiverId);
    const result = await this.privateMessageRepo
      .createQueryBuilder('pm')
      .select('COALESCE(MAX(pm.seq), 0)', 'maxSeq')
      .where('pm.chatId = :chatId', { chatId })
      .getRawOne<{ maxSeq: number }>();

    return (result?.maxSeq || 0) + 1;
  }

  async createPrivateMessage(
    senderId: number,
    receiverId: number,
    encryptedContent: string,
    seq: number,
    queryRunner?: QueryRunner, // Optional if used
    sendMessageDto?: SendPrivateMessageDto,
  ): Promise<PrivateMessage> {
    const chatId = PrivateMessageService.generateChatId(senderId, receiverId);

    const message = this.privateMessageRepo.create({
      chatId,
      senderId,
      receiverId,
      seq,
      encryptedContent,
      nonce: sendMessageDto?.nonce,
      encryptedMetaData: sendMessageDto?.encryptedMetaData,
      replyToMessageId: sendMessageDto?.replyToMessageId,
      sentAt: sendMessageDto?.sentAt,
      ephemeralPublicKey: sendMessageDto?.ephemeralPublicKey,
      messageIndex: sendMessageDto?.messageIndex,
      previousChainLength: sendMessageDto?.previousChainLength,
      chainKeyVersion: sendMessageDto?.chainKeyVersion,
    });

    return queryRunner
      ? queryRunner.manager.save(message)
      : this.privateMessageRepo.save(message);
  }

  private async saveMessage(
    sendMessageDto: SendPrivateMessageDto,
  ): Promise<PrivateMessage> {
    // Generate sequence number for this chat
    const lastMessage = await this.privateMessageRepo.findOne({
      where: { chatId: sendMessageDto.chatId },
      order: { seq: 'DESC' },
    });

    const seq = lastMessage ? lastMessage.seq + 1 : 1;

    const message = this.privateMessageRepo.create({
      chatId: sendMessageDto.chatId,
      senderId: sendMessageDto.senderId,
      receiverId: sendMessageDto.receiverId,
      seq,
      encryptedContent: sendMessageDto.encryptedContent,
      nonce: sendMessageDto.nonce,
      encryptedMetaData: sendMessageDto.encryptedMetaData,
      replyToMessageId: sendMessageDto.replyToMessageId,
      sentAt: sendMessageDto.sentAt,
      ephemeralPublicKey: sendMessageDto.ephemeralPublicKey,
      messageIndex: sendMessageDto.messageIndex,
      previousChainLength: sendMessageDto.previousChainLength,
      chainKeyVersion: sendMessageDto.chainKeyVersion,
    });

    const savedMessage = await this.privateMessageRepo.save(message);

    // Load relations for response
    const messageWithRelations = await this.privateMessageRepo.findOne({
      where: { id: savedMessage.id },
      relations: ['sender', 'receiver', 'replyTo'],
    });

    if (!messageWithRelations) {
      throw new Error('Saved message not found');
    }

    return messageWithRelations;
  }
  async getLastMessageForThread(
    member1Id: number,
    member2Id: number,
  ): Promise<PrivateMessage | null> {
    const chatId = PrivateMessageService.generateChatId(member1Id, member2Id);
    return this.privateMessageRepo.findOne({
      where: { chatId },
      order: { seq: 'DESC' },
    });
  }

  async getMessagesForChat(
    chatId: string,
    userId: number,
    page: number = 1,
    limit: number = 50,
  ): Promise<{ messages: PrivateMessage[]; total: number; hasMore: boolean }> {
    const skip = (page - 1) * limit;

    const queryBuilder = this.privateMessageRepo
      .createQueryBuilder('pm')
      .leftJoinAndSelect('pm.sender', 'sender')
      .leftJoinAndSelect('pm.receiver', 'receiver')
      .leftJoinAndSelect('pm.replyTo', 'replyTo')
      .where('pm.chatId = :chatId', { chatId })
      .andWhere(
        '((pm.senderId = :userId AND pm.deletedBySender = false) OR ' +
          '(pm.receiverId = :userId AND pm.deletedByReceiver = false))',
        { userId },
      )
      .andWhere('pm.deletedForEveryone = false')
      .orderBy('pm.seq', 'DESC')
      .skip(skip)
      .take(limit);

    const [messages, total] = await queryBuilder.getManyAndCount();
    const hasMore = skip + messages.length < total;

    return {
      messages: messages.reverse(), // Reverse to get chronological order
      total,
      hasMore,
    };
  }

  async markAsRead(
    chatId: string,
    readerId: number,
    messageIds: number[],
  ): Promise<void> {
    await this.privateMessageRepo.update(
      {
        chatId,
        id: { $in: messageIds } as any,
        receiverId: readerId,
      },
      {
        readByReceiver: true,
        readAt: new Date(),
      },
    );
  }

  async deleteMessageForMe(
    chatId: string,
    deleterId: number,
    messageId: number,
  ): Promise<void> {
    const message = await this.privateMessageRepo.findOne({
      where: { id: messageId, chatId },
    });

    if (!message) {
      throw new Error('Message not found');
    }

    if (message.senderId === deleterId) {
      await this.privateMessageRepo.update(messageId, {
        deletedBySender: true,
      });
    } else if (message.receiverId === deleterId) {
      await this.privateMessageRepo.update(messageId, {
        deletedByReceiver: true,
      });
    }
  }

  async deleteMessagesForEveryone(
    chatId: string,
    deleterId: number,
    messageIds: number[],
  ): Promise<void> {
    // Only sender can delete for everyone
    await this.privateMessageRepo.update(
      {
        id: { $in: messageIds } as any,
        chatId,
        senderId: deleterId,
      },
      {
        deletedForEveryone: true,
      },
    );
  }

  // Utility method to generate chat ID
  static generateChatId(userId1: number, userId2: number): string {
    const [smallerId, largerId] = [userId1, userId2].sort((a, b) => a - b);
    return `${smallerId}_${largerId}`;
  }
}
